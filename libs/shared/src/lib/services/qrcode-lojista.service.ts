import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AbstractService } from './abstract.service';
import { resolve } from '@utils/resolve.util';

// Interface para request de criar/vincular/editar/cancelar cartão QR code
export interface CriaVinculaEditaOuCancelaCartaoQrCodeVO {
  id?: number;
  uuidAssinado?: string;
  chavePreVinculacao?: string;
  apelido?: string;
  idContaVinculada?: number;
  status?: number;
}

// Interface para response de imagem do QR code
export interface ImagemCartaoQrCodeVO {
  id: number;
  imagemBase64: string;
}

// Interface para entidade CartaoQrCode
export interface CartaoQrCode {
  id: number;
  uuid: string;
  idContaVinculada?: number;
  apelido?: string;
  chavePreVinculacao?: string;
  status: number;
  idUsuarioInclusao: number;
  idPortadorInclusao: number;
  dtHrInclusao: string;
  idUsuarioVinculacao?: number;
  idPortadorVinculacao?: number;
  dtHrVinculacao?: string;
  idUsuarioCancelamento?: number;
  idPortadorCancelamento?: number;
  dtHrCancelamento?: string;
}

// Interface para VO de listagem
export interface CartaoQrCodeVO {
  id: number;
  uuid: string;
  idContaVinculada?: number;
  chavePreVinculacao?: string;
  apelido?: string;
  idStatus: number;
  nomeCompleto?: string;
  documento?: string;
}

export interface DadosQRCodeLojistaResponse {
  valor: number;
  nomeEstabelecimento: string;
  idEstabelecimento: number;
  idQrCode: number;
  utilizado: boolean;
}

export interface PagarQRCodeLojistaRequest {
  idQrCode: number;
  valorPagamento: number;
  idCredencial: number;
  idContaOrigem: number;
  pinCredencial?: string;
  tokenSms?: string;
  metodoSeguranca?: number;
}

export interface PagarQRCodeLojistaResponse {
  idTransacao: string;
  valorPago: number;
  nomeEstabelecimento: string;
  dataHoraPagamento: Date;
  stan: string;
  status: string;
  mensagem: string;
}

@Injectable({
  providedIn: 'root'
})
export class QrcodeLojistaService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'api', http);
  }

  /**
   * Lê os dados de um QR code do lojista
   * @param qrcode String do QR code lido
   * @returns Observable com os dados do QR code
   */
  lerQRCodeLojista(qrcode: string): Observable<{ qrcode: DadosQRCodeLojistaResponse; msg?: string }> {
    const request = {
      qrcode: qrcode
    };
    const url = resolve('valloo://lerQRCodeLojista');
    return this.http.post<{ qrcode: DadosQRCodeLojistaResponse; msg?: string }>(url, request);
  }

  /**
   * Processa o pagamento via QR code do lojista
   * @param pagamento Dados do pagamento
   * @returns Observable com a resposta do pagamento
   */
  pagarQRCodeLojista(pagamento: PagarQRCodeLojistaRequest): Observable<PagarQRCodeLojistaResponse> {
    const url = resolve('valloo://pagarQRCodeLojista');
    return this.http.post<PagarQRCodeLojistaResponse>(url, pagamento);
  }

  /**
   * Obtém o QR code permanente do estabelecimento
   * @returns Observable com o QR code do estabelecimento ou null se não existir
   */
  obterQRCodeEstabelecimento(): Observable<ObterQRCodeEstabelecimentoResponse> {
    const url = resolve('valloo://obterQRCodeEstabelecimento');
    return this.http.get<ObterQRCodeEstabelecimentoResponse>(url);
  }
}

// Para compatibilidade com código existente - DEPRECATED
export interface QRCodeLojistaResponse {
  qrCode: string;
  idQrCode: number;
  valor?: number;
}

export interface ObterQRCodeEstabelecimentoResponse {
  qrCode?: string;
  idQrCode?: number;
  ativo: boolean;
  dataGeracao?: Date;
}
