/* eslint-disable @angular-eslint/component-class-suffix */
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NavController, ModalController, Platform } from '@ionic/angular';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';
import { ModalErroComponent, ModalSegurancaComponent } from '@mobile/modals';
import { QrcodeLojistaService, DadosQRCodeLojistaResponse, PagarQRCodeLojistaRequest, AuthService, Usuario, Credencial, Conta } from '@mobile/shared';
import { Camera } from '@capacitor/camera';
import type { CapacitorBarcodeScannerScanResult } from '@capacitor/barcode-scanner/dist/esm/definitions';
import {
  CapacitorBarcodeScanner,
  CapacitorBarcodeScannerAndroidScanningLibrary,
  CapacitorBarcodeScannerCameraDirection,
  CapacitorBarcodeScannerScanOrientation,
  CapacitorBarcodeScannerTypeHint
} from '@capacitor/barcode-scanner';
// TODO: Instalar e migrar para '@capacitor-mlkit/barcode-scanning' conforme mobile-escoteiros

@Component({
  selector: 'app-cobrar-qrcode',
  templateUrl: './cobrar-qrcode.page.html',
  styleUrls: ['./cobrar-qrcode.page.scss']
})
export class CobrarQrcodePage implements OnInit {

  etapaAtual: 'valor' | 'scanner' | 'sucesso' | 'erro' = 'valor';

  formValor = new FormGroup({
    valor: new FormControl('', [Validators.required, this.validarValorPositivo.bind(this)])
  });

  // Removido formConfirmacao - agora usa o modal de senha

  valorPagamento = 0;
  qrCodeData: any = null;
  dadosTransacao: any = {};
  isProcessando = false;

  usuario!: Usuario;
  primeiraCredencial!: Credencial;
  primeiraConta!: Conta;

  constructor(
    private router: Router,
    private navController: NavController,
    private modalController: ModalController,
    private platform: Platform,
    private qrcodeLojistaService: QrcodeLojistaService,
    private authService: AuthService
  ) {}

    ngOnInit() {
    this.etapaAtual = 'valor';

      // Buscar dados do usuário logado
      this.usuario = this.authService.getUser();

      if (!this.usuario || !this.usuario.credenciais || this.usuario.credenciais.length === 0) {
        throw new Error('Dados do usuário não encontrados. Faça login novamente.');
      }

      // Pegar a primeira credencial e conta disponível
      this.primeiraCredencial = this.usuario.credenciais[0];
      this.primeiraConta = this.primeiraCredencial.contas?.[0];

      if (!this.primeiraCredencial || !this.primeiraConta) {
        throw new Error('Credencial ou conta não encontrada para realizar o pagamento.');
      }

  }

  voltar() {
    if (this.etapaAtual === 'valor') {
      this.navController.back();
    } else if (this.etapaAtual === 'scanner') {
      this.etapaAtual = 'valor';
    } else {
      this.etapaAtual = 'valor';
      this.formValor.reset();
      this.qrCodeData = null;
    }
  }

  confirmarValor() {
    // Marcar o campo como tocado para mostrar validação
    this.formValor.get('valor')?.markAsTouched();

    if (this.formValor.invalid) {
      return;
    }

    const valorFormatado = this.formValor.get('valor')?.value;
    if (!valorFormatado) {
      return;
    }

    const valor = this.extrairValorNumerico(valorFormatado);
    if (valor <= 0) {
      return;
    }

    this.valorPagamento = valor;
    this.abrirScanner();
  }

  async abrirScanner() {
    this.etapaAtual = 'scanner';
    await this.lerQrCode();
  }

  async scan() {
    try {
      const options: any = {
        hint: CapacitorBarcodeScannerTypeHint.QR_CODE,
        scanOrientation: CapacitorBarcodeScannerScanOrientation.LANDSCAPE,
        cameraDirection: CapacitorBarcodeScannerCameraDirection.BACK,
        scanInstructions: 'Posicione o QRCode na marcação',
        android: {
          scanningLibrary: CapacitorBarcodeScannerAndroidScanningLibrary.ZXING
        }
      };
      return CapacitorBarcodeScanner.scanBarcode(options);
    } catch (e: any) {
      if (e.errorMessage == 'scan canceled.' || e.message == 'scan canceled.') {
        this.etapaAtual = 'valor';
      }
      return null;
    }
  }

  async lerQrCode() {
    if (!this.platform.is('hybrid')) {
      // Simulação para desenvolvimento
      await this.avancar('afbbd8f6-bd37-44da-9c8a-ce649f3df8fa:731c08ba684655299185e37745b75bc9e5dc62d1b08383600859d0114d52c106');
    } else {
      const permission = await Camera.checkPermissions();
      if (permission.camera == 'denied') {
        await Camera.requestPermissions({ permissions: ['camera'] });
      }

      const result: CapacitorBarcodeScannerScanResult | null = await this.scan();
      if (result) {
        await this.avancar(result.ScanResult);
      }
    }
  }

  async avancar(uuid: string) {
    try {
      this.isProcessando = true;

      let qrCodeData: any;

      // Se for ambiente de desenvolvimento web local, pular a chamada do serviço
      if (!this.platform.is('hybrid')) {
        // Simular dados do QR code para desenvolvimento
        qrCodeData = {
          idQrCode: 'dev-qrcode-123',
          idEstabelecimento: 'dev-estabelecimento-456',
          nomeEstabelecimento: 'Estabelecimento de Desenvolvimento',
          valor: null, // Usar o valor informado pelo usuário
          utilizado: false
        };
      } else {
        // Ambiente normal - fazer a chamada do serviço
        const response = await this.qrcodeLojistaService.lerQRCodeLojista(uuid).toPromise();

        if (!response || !response.qrcode) {
          throw new Error(response?.msg || 'QR Code não encontrado');
        }

        qrCodeData = response.qrcode;

        if (qrCodeData.utilizado) {
          throw new Error('QR Code já foi utilizado');
        }
      }

      // Verificar se o valor informado confere com o valor do QR code (se houver)
      if (qrCodeData.valor && qrCodeData.valor !== this.valorPagamento) {
        const confirmarValor = confirm(
          `O valor do QR Code é R$ ${qrCodeData.valor.toFixed(2)}. Deseja continuar com este valor?`
        );
        if (confirmarValor) {
          this.valorPagamento = qrCodeData.valor;
          this.formValor.get('valor')?.setValue(this.formatarValor(this.valorPagamento));
        } else {
          this.isProcessando = false;
          return;
        }
      }

      this.dadosTransacao = {
        estabelecimento: qrCodeData.nomeEstabelecimento,
        valor: this.valorPagamento,
        data: new Date(),
        identificador: 'QRC' + Date.now(),
        idQrCode: qrCodeData.idQrCode,
        idEstabelecimento: qrCodeData.idEstabelecimento
      };

      this.isProcessando = false;

      // Após ler o QR code, vai direto para confirmação da senha
      await this.confirmarPagamento();
    } catch (erro: any) {
      this.isProcessando = false;
      this.apresentarMensagemErro(erro.msg || erro.message || 'Houve um erro inesperado ao consultar o QR Code.');
    }
  }

  async apresentarMensagemErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ocorreu um erro',
        mensagem: message,
        tituloBotao: 'Tentar novamente'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    await modal.onWillDismiss();
    this.etapaAtual = 'valor';
  }

  async confirmarPagamento() {
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: 1, // 1 = Senha do cartão (4 dígitos)
        idCredencial: this.primeiraCredencial.idCredencial,
        documento: this.usuario.documento,
        telefoneCelular: `${this.usuario.dddTelefoneCelular}${this.usuario.telefoneCelular}`
      },
      cssClass: 'modal-half-default'
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();

    if (data === true) {
      // Senha confirmada, processar pagamento
      this.processarPagamento();
    }
  }

  private async processarPagamento() {
    this.isProcessando = true;

    try {

      const pagamento: PagarQRCodeLojistaRequest = {
        idQrCode: this.dadosTransacao.idQrCode,
        valorPagamento: this.valorPagamento,
        idCredencial: this.primeiraCredencial.idCredencial,
        idContaOrigem: this.primeiraConta.idConta,
        metodoSeguranca: 1 // Senha do cartão
      };

      const response = await this.qrcodeLojistaService.pagarQRCodeLojista(pagamento).toPromise();

      if (response && response.status === 'SUCESSO') {
        // Atualizar dados da transação com os dados retornados
        this.dadosTransacao.idTransacao = response.idTransacao;
        this.dadosTransacao.stan = response.stan;
        this.dadosTransacao.dataHoraPagamento = response.dataHoraPagamento;

        this.etapaAtual = 'sucesso';
      } else {
        throw new Error(response?.mensagem || 'Erro ao processar pagamento');
      }

    } catch (erro: any) {
      this.apresentarMensagemErro(erro.msg || erro.message || 'Erro inesperado ao processar o pagamento');
    } finally {
      this.isProcessando = false;
    }
  }

  tentarNovamente() {
    this.etapaAtual = 'scanner';
    this.qrCodeData = null;
  }

  irParaInicio() {
    this.router.navigate(['/inicio']);
  }

  voltarAoInicio() {
    this.etapaAtual = 'valor';
    this.formValor.reset();
    this.qrCodeData = null;
    this.dadosTransacao = {};
  }



  formatarValor(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  }

  formatarData(data: Date): string {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(data);
  }

  formatarValorInput(event: any): void {
    let valor = event.detail.value;

    // Remover caracteres não numéricos
    valor = valor.replace(/\D/g, '');

    if (valor.length === 0) {
      this.formValor.get('valor')?.setValue('');
      this.formValor.get('valor')?.markAsTouched();
      return;
    }

    // Converter para centavos
    const valorNumerico = parseInt(valor, 10) / 100;

    // Validar se o valor é maior que zero
    if (valorNumerico > 0) {
      // Formatar como moeda brasileira
      const valorFormatado = new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(valorNumerico);

      this.formValor.get('valor')?.setValue(valorFormatado);
      this.formValor.get('valor')?.markAsTouched();
    }
  }

  private extrairValorNumerico(valorFormatado: string): number {
    // Remove R$, espaços, pontos e substitui vírgula por ponto
    const valor = valorFormatado
      .replace(/R\$\s?/g, '')
      .replace(/\./g, '')
      .replace(',', '.');

    return parseFloat(valor) || 0;
  }

  private validarValorPositivo(control: FormControl): {[key: string]: any} | null {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const valor = this.extrairValorNumerico(control.value);
    return valor > 0 ? null : { valorInvalido: true };
  }
}
