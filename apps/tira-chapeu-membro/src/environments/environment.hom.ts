// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.
import { BannerTitleComponent, CartaoFieldComponent } from '@mobile/components';
import { AppEnvironment, merge } from '@mobile/shared';

export const environment: AppEnvironment = merge({
  production: false,
  appId: 'br.com.tirachapeu_membro', // TODO: verificar appId
  app: 'tiraChapeuMembro',
  appName: 'PalaClub Membro',
  env: 'hom',
  idInstituicao: 1301,
  idProcessadora: 10,
  idProgramaFidelidade: 2,
  idProdutoInstituicao: 130101,
  isbp: 23273917,
  validarCadastroComCaf: false,
  tokenCaf: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenFaceLiveness: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenCafBeta: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTljNzMxYWUyMzY2YzAwMDhiOWIzM2UifQ.QoddeSSVsQrm1DeYyCnhW7Wny928_EtiagHPBchsIuo',
  idGrupoAcesso: 61,
  idGrupoAcessoPj: 62,
  idAplicativo: 25,
  urlMotiva: 'https://motiva.itspay-hom.com.br',
  onesignalAppId: '************************************',
  hosts: {
    valloo: {
      host: 'localhost',
      protocol: 'http',
      port: '28080',
      root: '/api/api',
    }
  },
  buttonType: 'squared',
  buttonLayout: 'grid',
  homeVersion: 'v2',
  homeSubVersion: '1',
  showChat: false,
  showIcon: true,
  showTitleDots: false,
  tituloAjuda: 'Chat de atendimento',
  tipoLoginPj: '',
  tipoLoginPf: 'TIRA_CHAPEU_MULTIBENEFICIOS',
  urlApple: 'https://apps.apple.com/us/app/palaclub-membro/id6736953685',
  corFuncionalidades: false,
  telefoneContato: '(62) 98152-5804',
  navigationRules: [
    {
      origin: 'inicio',
      destination: '/inicio-v2'
    },
    {
      origin: 'login-criar-conta',
      destination: '/criar-conta'
    },
    {
      origin: 'validar-conta-dados-pessoais',
      destination: '/confirmar-dados-pessoais'
    },
    {
      origin: 'confirmar-criar-conta',
      destination: '/validar-conta-token'
    },
    {
      origin: 'confirmar-endereco',
      destination: '/cadastrar-senha'
    }
  ],
  componentExtensions: {
    'criar-conta': {
      afterFields: [
        {
          component: CartaoFieldComponent,
          order: 1
        }
      ]
    },
    'banner-title': {
      afterFields: [
        {
          component: BannerTitleComponent,
          order: 1
        }
      ]
    }
  }
});

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
