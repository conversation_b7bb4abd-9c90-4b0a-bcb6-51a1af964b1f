import { Component } from '@angular/core';
import {
  AuthService,
  ContaService,
  environment,
  MetodoSegurancaEnum,
  TokenFuncionalidadeService,
  Usuario
} from '@lib/shared';
import { Router } from '@angular/router';
import { ModalErroComponent, ModalSucessoComponent } from '@lib/modals';
import { ModalController } from '@ionic/angular';
import { loading } from '@utils/loading.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { numberMask } from '@utils/masks.util';
import { TextUtil } from '@utils/text.util';
import { lastValueFrom } from 'rxjs';
import { HashUtil } from '@utils/hash.util';

@Component({
  selector: 'lojista-pagar',
  templateUrl: './pagar.component.html',
  styleUrl: './pagar.component.scss',
})
export class PagarComponent {
  dados: any;
  usuario: Usuario;
  readonly numberMaskOptions: MaskitoOptions = numberMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();
  formValor = new FormGroup({
    valor: new FormControl('', [Validators.required, Validators.minLength(4)])
  });
  METODO_SENHA_CARTAO = MetodoSegurancaEnum.SenhaCartao;
  METODO_TOKEN_SMS = MetodoSegurancaEnum.TokenSms;

  constructor(
    private authService: AuthService,
    private router: Router,
    private contaService: ContaService,
    private modalController: ModalController,
    private tokenFuncionalidadeService: TokenFuncionalidadeService
  ) {
    this.dados = this.router.getCurrentNavigation()?.extras?.state?.['dados'];
    this.usuario = this.authService.getUser();
    if (this.dados.metodoSeguranca == MetodoSegurancaEnum.TokenSms) {
      this.enviarSms();
    }
    if (this.dados.metodoSeguranca == MetodoSegurancaEnum.NaoVerificar) {
      this.formValor.get('valor')?.clearValidators();
    }
  }

  async confirmar() {
    const usuario = this.authService.getUser();
    const payload: any = {
      idContaOrigem: this.dados.idConta,
      idCredencial: this.dados.idCredencial,
      idContaDestino: usuario.conta.idConta,
      valorTransferencia: this.dados.valor
    };
    const valor = this.formValor.getRawValue().valor;
    if (this.dados.metodoSeguranca == MetodoSegurancaEnum.SenhaCartao) {
      let senha = valor || 0;
      const senhaToken = senha + this.authService.getToken();
      payload.pinCredencial = HashUtil.hash(senhaToken);
    }
    if (this.dados.metodoSeguranca == MetodoSegurancaEnum.TokenSms) {
      payload.tokenSms = valor;
    }

    await loading(
      this.contaService.cobrarQrcode(payload).subscribe({
        next: async () => {
          const modal = await this.apresentarMensagemSucesso({
            titulo: 'Compra realizada com sucesso.',
            tituloBotao: 'Realizar nova venda',
            tituloBotaoSecundario: 'Voltar para início'
          });
          if (modal.role === 'primaria') {
            return this.router.navigate(['/cobrar-qrcode'])
          }
          if (modal.role === 'secundaria') {
            return this.router.navigate(['/inicio'])
          }
        },
        error: (erro: any) => {
          this.apresentarMensagemErro(erro.msg || 'Ocorreu um erro inesperado. Tente novamente!')
        }
      })
    );
  }

  async apresentarMensagemSucesso(props: any) {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: props
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    return data;
  }

  async apresentarMensagemErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ocorreu um erro',
        mensagem: message,
        tituloBotao: 'Tentar novamente'
      }
    });
    await modal.present();
  }

  enviarSms() {
    const dados = {
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
      idFuncionalidade: 1,
      documento: TextUtil.removeNotDigit(this.dados.documento),
      tipoLogin: this.dados.tipoLogin,
      chave: this.dados.telefoneCelular
    };
    return lastValueFrom(this.tokenFuncionalidadeService.enviarSms(dados))
  }

}
